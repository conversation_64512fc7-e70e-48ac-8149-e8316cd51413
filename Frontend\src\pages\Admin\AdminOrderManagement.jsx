import React, { useState, useEffect } from "react";
import { toast } from "react-toastify";
import AdminLayout from "../../components/admin/AdminLayout";
import Table from "../../components/common/Table";
import AdminTableActions from "../../components/admin/AdminTableActions";
import adminOrderService from "../../services/adminOrderService";
import "../../styles/AdminOrderManagement.css";

// Icons
import {
  FaSearch,
  FaEye,
  FaEdit,
  FaTrash,
  FaTimes,
  FaDownload,
  FaSync,
  FaShoppingCart,
  FaDollarSign,
  FaCalendarAlt,
  FaUser,
  FaFileAlt
} from "react-icons/fa";

const AdminOrderManagement = () => {
  
  // Local state
  const [orders, setOrders] = useState([]);
  const [selectedOrders, setSelectedOrders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState({
    status: "all",
    orderType: "all",
    paymentStatus: "all",
    dateFrom: "",
    dateTo: ""
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);

  // Helper function to transform API data to component format
  const transformOrderData = (apiOrders) => {
    return apiOrders.map(order => ({
      id: order._id,
      orderNumber: `ORD-${order._id.slice(-8).toUpperCase()}`,
      buyer: {
        name: `${order.buyer?.firstName || ''} ${order.buyer?.lastName || ''}`.trim() || 'Unknown',
        email: order.buyer?.email || 'N/A'
      },
      seller: {
        name: `${order.seller?.firstName || ''} ${order.seller?.lastName || ''}`.trim() || 'Unknown',
        email: order.seller?.email || 'N/A'
      },
      content: {
        title: order.content?.title || 'Unknown Content',
        type: order.content?.contentType || 'Unknown'
      },
      orderType: order.orderType,
      amount: order.amount,
      platformFee: order.platformFee,
      sellerEarnings: order.sellerEarnings,
      status: order.status,
      paymentStatus: order.paymentStatus,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt || order.createdAt,
      originalOrder: order // Keep reference to original order data
    }));
  };

  useEffect(() => {
    fetchOrders();
  }, [pagination.page, pagination.limit, searchTerm, filters]);

  const fetchOrders = async () => {
    setLoading(true);
    try {
      const response = await adminOrderService.getAllOrders({
        page: pagination.page,
        limit: pagination.limit,
        search: searchTerm,
        status: filters.status,
        paymentStatus: filters.paymentStatus,
        orderType: filters.orderType,
        dateFrom: filters.dateFrom,
        dateTo: filters.dateTo
      });

      if (response.success) {
        const transformedOrders = transformOrderData(response.data);
        setOrders(transformedOrders);
        setPagination(prev => ({
          ...prev,
          total: response.pagination.total,
          totalPages: response.pagination.pages
        }));
      } else {
        toast.error("Failed to fetch orders");
      }
    } catch (error) {
      console.error("Error fetching orders:", error);
      toast.error(error.response?.data?.message || "Failed to fetch orders");
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (value) => {
    setSearchTerm(value);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleFilterChange = (filterType, value) => {
    setFilters(prev => ({ ...prev, [filterType]: value }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleViewOrder = (order) => {
    setSelectedOrder(order);
    setShowDetailModal(true);
  };

  const handleUpdateOrderStatus = async (orderId, newStatus, notes = '') => {
    try {
      const response = await adminOrderService.updateOrderStatus(orderId, {
        status: newStatus,
        notes
      });

      if (response.success) {
        toast.success(`Order status updated to ${newStatus}`);
        fetchOrders();
      } else {
        toast.error("Failed to update order status");
      }
    } catch (error) {
      console.error("Error updating order status:", error);
      toast.error(error.response?.data?.message || "Failed to update order status");
    }
  };

  const handleDeleteOrder = async (orderId) => {
    if (window.confirm("Are you sure you want to delete this order?")) {
      try {
        const response = await adminOrderService.deleteOrder(orderId);

        if (response.success) {
          toast.success("Order deleted successfully");
          fetchOrders();
        } else {
          toast.error("Failed to delete order");
        }
      } catch (error) {
        console.error("Error deleting order:", error);
        toast.error(error.response?.data?.message || "Failed to delete order");
      }
    }
  };

  const handleBulkAction = async (action, orderIds) => {
    try {
      switch (action) {
        case "delete":
          if (window.confirm(`Are you sure you want to delete ${orderIds.length} orders?`)) {
            const response = await adminOrderService.bulkDeleteOrders(orderIds);

            if (response.success) {
              toast.success(`${orderIds.length} orders deleted successfully`);
              setSelectedOrders([]);
              fetchOrders();
            } else {
              toast.error("Failed to delete orders");
            }
          }
          break;
        case "updateStatus":
          // This would require a status selection modal
          // For now, we'll implement individual status updates
          toast.info("Please update order status individually");
          break;
        default:
          break;
      }
    } catch (error) {
      console.error("Error performing bulk action:", error);
      toast.error(error.response?.data?.message || `Failed to perform bulk action: ${action}`);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleExport = async () => {
    try {
      const response = await adminOrderService.exportOrders({
        format: 'csv',
        status: filters.status,
        dateFrom: filters.dateFrom,
        dateTo: filters.dateTo
      });

      if (response.success) {
        // Create and download the file
        const blob = new Blob([response.data], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `orders-export-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        toast.success("Orders exported successfully");
      } else {
        toast.error("Failed to export orders");
      }
    } catch (error) {
      console.error("Error exporting orders:", error);
      toast.error(error.response?.data?.message || "Failed to export orders");
    }
  };

  const getStatusBadge = (status) => {
    const statusClasses = {
      'Completed': 'status-completed',
      'Processing': 'status-processing',
      'Pending': 'status-pending',
      'Cancelled': 'status-cancelled',
      'Refunded': 'status-refunded'
    };
    
    return (
      <span className={`status-badge ${statusClasses[status] || 'status-default'}`}>
        {status}
      </span>
    );
  };

  const columns = [
    {
      key: "orderNumber",
      label: "Order #",
      sortable: true,
      render: (order) => (
        <div className="order-number">
          <FaShoppingCart className="order-icon" />
          <span>{order.orderNumber}</span>
        </div>
      )
    },
    {
      key: "buyer",
      label: "Buyer",
      sortable: true,
      render: (order) => (
        <div className="user-info">
          <div className="user-name">{order.buyer.name}</div>
          <div className="user-email">{order.buyer.email}</div>
        </div>
      )
    },
    {
      key: "seller",
      label: "Seller",
      sortable: true,
      render: (order) => (
        <div className="user-info">
          <div className="user-name">{order.seller.name}</div>
          <div className="user-email">{order.seller.email}</div>
        </div>
      )
    },
    {
      key: "content",
      label: "Content",
      render: (order) => (
        <div className="content-info">
          <div className="content-title">{order.content.title}</div>
          <div className="content-type">{order.content.type}</div>
        </div>
      )
    },
    {
      key: "orderType",
      label: "Type",
      sortable: true,
      render: (order) => (
        <span className={`order-type order-type-${order.orderType.toLowerCase()}`}>
          {order.orderType}
        </span>
      )
    },
    {
      key: "amount",
      label: "Amount",
      sortable: true,
      render: (order) => (
        <div className="amount-info">
          <div className="total-amount">{formatCurrency(order.amount)}</div>
          <div className="platform-fee">Fee: {formatCurrency(order.platformFee)}</div>
        </div>
      )
    },
    {
      key: "status",
      label: "Status",
      sortable: true,
      render: (order) => getStatusBadge(order.status)
    },
    {
      key: "createdAt",
      label: "Created",
      sortable: true,
      render: (order) => (
        <div className="date-info">
          <FaCalendarAlt className="date-icon" />
          <span>{formatDate(order.createdAt)}</span>
        </div>
      )
    },
    {
      key: "actions",
      label: "Actions",
      render: (order) => (
        <div className="action-buttons">
          <button
            className="btn-icon btn-view"
            onClick={() => handleViewOrder(order)}
            title="View Details"
          >
            <FaEye />
          </button>
          <button
            className="btn-icon btn-edit"
            onClick={() => {
              const newStatus = prompt("Enter new status (Pending, Processing, Completed, Cancelled, Refunded):");
              if (newStatus && ["Pending", "Processing", "Completed", "Cancelled", "Refunded"].includes(newStatus)) {
                handleUpdateOrderStatus(order.id, newStatus);
              } else if (newStatus) {
                toast.error("Invalid status. Please use: Pending, Processing, Completed, Cancelled, or Refunded");
              }
            }}
            title="Update Status"
          >
            <FaEdit />
          </button>
          <button
            className="btn-icon btn-delete"
            onClick={() => handleDeleteOrder(order.id)}
            title="Delete Order"
          >
            <FaTrash />
          </button>
        </div>
      )
    }
  ];

  return (
    <AdminLayout>
      <div className="AdminOrderManagement">
        <div className="AdminOrderManagement__header">
          <div className="header-content">
            <div className="header-text">
              <h1>Order Management</h1>
              <p>Manage and monitor all orders in the system</p>
            </div>
            <div className="header-actions">
              <button className="btn btn-secondary" onClick={fetchOrders}>
                <FaSync /> Refresh
              </button>
              <button className="btn btn-primary" onClick={handleExport}>
                <FaDownload /> Export
              </button>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="AdminOrderManagement__filters">
          <div className="search-section">
            <div className="search-input">
              <FaSearch className="search-icon" />
              <input
                type="text"
                placeholder="Search orders by number, buyer, seller, or content..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>
          </div>

          <div className="filter-section">
            <div className="filter-group">
              <label>Status:</label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange("status", e.target.value)}
              >
                <option value="all">All Status</option>
                <option value="Pending">Pending</option>
                <option value="Processing">Processing</option>
                <option value="Completed">Completed</option>
                <option value="Cancelled">Cancelled</option>
                <option value="Refunded">Refunded</option>
              </select>
            </div>

            <div className="filter-group">
              <label>Order Type:</label>
              <select
                value={filters.orderType}
                onChange={(e) => handleFilterChange("orderType", e.target.value)}
              >
                <option value="all">All Types</option>
                <option value="Fixed">Fixed Price</option>
                <option value="Auction">Auction</option>
                <option value="Custom">Custom Request</option>
              </select>
            </div>

            <div className="filter-group">
              <label>Payment:</label>
              <select
                value={filters.paymentStatus}
                onChange={(e) => handleFilterChange("paymentStatus", e.target.value)}
              >
                <option value="all">All Payments</option>
                <option value="Completed">Completed</option>
                <option value="Pending">Pending</option>
                <option value="Failed">Failed</option>
                <option value="Refunded">Refunded</option>
              </select>
            </div>

            <div className="filter-group">
              <label>Date From:</label>
              <input
                type="date"
                value={filters.dateFrom}
                onChange={(e) => handleFilterChange("dateFrom", e.target.value)}
              />
            </div>

            <div className="filter-group">
              <label>Date To:</label>
              <input
                type="date"
                value={filters.dateTo}
                onChange={(e) => handleFilterChange("dateTo", e.target.value)}
              />
            </div>
          </div>
        </div>

        {/* Table Actions */}
        <AdminTableActions
          selectedItems={selectedOrders}
          onBulkAction={handleBulkAction}
          actions={[
            { key: "delete", label: "Delete Selected", icon: <FaTrash />, variant: "danger" }
          ]}
        />

        {/* Orders Table */}
        <div className="AdminOrderManagement__table">
          <Table
            data={orders}
            columns={columns}
            loading={loading}
            selectedItems={selectedOrders}
            onSelectionChange={setSelectedOrders}
            pagination={pagination}
            onPageChange={(page) => setPagination(prev => ({ ...prev, page }))}
            onLimitChange={(limit) => setPagination(prev => ({ ...prev, limit, page: 1 }))}
            emptyMessage="No orders found"
            selectable={true}
          />
        </div>

        {/* Order Detail Modal */}
        {showDetailModal && selectedOrder && (
          <div className="modal-overlay" onClick={() => setShowDetailModal(false)}>
            <div className="modal-content order-detail-modal" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h3>Order Details - {selectedOrder.orderNumber}</h3>
                <button className="modal-close" onClick={() => setShowDetailModal(false)}>
                  <FaTimes />
                </button>
              </div>
              <div className="modal-body">
                <div className="order-detail-grid">
                  <div className="detail-section">
                    <h4><FaUser /> Buyer Information</h4>
                    <p><strong>Name:</strong> {selectedOrder.buyer.name}</p>
                    <p><strong>Email:</strong> {selectedOrder.buyer.email}</p>
                  </div>
                  <div className="detail-section">
                    <h4><FaUser /> Seller Information</h4>
                    <p><strong>Name:</strong> {selectedOrder.seller.name}</p>
                    <p><strong>Email:</strong> {selectedOrder.seller.email}</p>
                  </div>
                  <div className="detail-section">
                    <h4><FaFileAlt /> Content Information</h4>
                    <p><strong>Title:</strong> {selectedOrder.content.title}</p>
                    <p><strong>Type:</strong> {selectedOrder.content.type}</p>
                  </div>
                  <div className="detail-section">
                    <h4><FaDollarSign /> Payment Information</h4>
                    <p><strong>Total Amount:</strong> {formatCurrency(selectedOrder.amount)}</p>
                    <p><strong>Platform Fee:</strong> {formatCurrency(selectedOrder.platformFee)}</p>
                    <p><strong>Seller Earnings:</strong> {formatCurrency(selectedOrder.sellerEarnings)}</p>
                    <p><strong>Status:</strong> {getStatusBadge(selectedOrder.status)}</p>
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button className="btn btn-secondary" onClick={() => setShowDetailModal(false)}>
                  Close
                </button>
                <button className="btn btn-primary">
                  Edit Order
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default AdminOrderManagement;
